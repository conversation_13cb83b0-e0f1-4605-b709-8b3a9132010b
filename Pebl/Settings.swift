//
//  Settings.swift
//  Pebl
//
//  Created by AI Assistant on 6/26/25.
//

import Foundation

/// User settings and preferences for the app
struct AppSettings: Codable {
    var messageExpirationDays: Int = 60
    var isDarkModeEnabled: Bool = false
    var autoDeleteExpiredMessages: Bool = true
    
    // MARK: - Singleton Instance
    static let shared = SettingsManager()
}

/// Manages app settings persistence and access
class SettingsManager: ObservableObject {
    @Published var settings = AppSettings()
    
    private let settingsFileName = "app_settings.json"
    
    init() {
        loadSettings()
    }
    
    // MARK: - Persistence Methods
    
    private func getSettingsFileURL() -> URL? {
        let fileManager = FileManager.default
        guard let documentDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }
        return documentDirectory.appendingPathComponent(settingsFileName)
    }
    
    func saveSettings() {
        guard let fileURL = getSettingsFileURL() else {
            print("Error: Could not get settings file URL")
            return
        }
        
        do {
            let data = try JSONEncoder().encode(settings)
            try data.write(to: fileURL)
            print("Settings saved successfully")
        } catch {
            print("Error saving settings: \(error)")
        }
    }
    
    func loadSettings() {
        guard let fileURL = getSettingsFileURL() else {
            print("Error: Could not get settings file URL")
            return
        }
        
        let fileManager = FileManager.default
        guard fileManager.fileExists(atPath: fileURL.path) else {
            print("Settings file does not exist, using defaults")
            return
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            settings = try JSONDecoder().decode(AppSettings.self, from: data)
            print("Settings loaded successfully")
        } catch {
            print("Error loading settings: \(error), using defaults")
            // Keep default settings if loading fails
        }
    }
    
    // MARK: - Convenience Methods
    
    func updateMessageExpirationDays(_ days: Int) {
        settings.messageExpirationDays = days
        saveSettings()
    }
    
    func toggleDarkMode() {
        settings.isDarkModeEnabled.toggle()
        saveSettings()
    }
    
    func updateAutoDeleteExpiredMessages(_ enabled: Bool) {
        settings.autoDeleteExpiredMessages = enabled
        saveSettings()
    }
}

// MARK: - Settings Extensions

extension AppSettings {
    /// Returns true if a message should be considered expired based on current settings
    func isMessageExpired(_ message: Message) -> Bool {
        return message.isOlderThan(days: messageExpirationDays)
    }
    
    /// Returns the cutoff date for message expiration
    var messageExpirationCutoffDate: Date {
        let calendar = Calendar.current
        return calendar.date(byAdding: .day, value: -messageExpirationDays, to: Date()) ?? Date()
    }
}
